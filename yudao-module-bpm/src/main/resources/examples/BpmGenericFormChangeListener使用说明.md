# BpmGenericFormChangeListener 使用说明

## 概述

`BpmGenericFormChangeListener` 是一个通用的表单变更审批监听器，用于在变更流程审批通过后，自动更新台账流程实例的数据。

## 新增功能：流程实例ID映射

在最新版本中，新增了 `processInstanceIdField` 配置参数，可以将当前变更流程的实例ID保存到台账流程实例中，方便后续追溯和关联。

## 配置参数说明

### 基础配置
- `relationField`: 关联字段名，用于查找台账流程实例
- `changeRecordField`: 变更记录字段名，用于保存变更历史
- `processInstanceIdField`: **新增** 流程实例ID字段名，用于保存当前变更流程的实例ID

### 高级配置
- `fieldMapping`: 字段映射关系，将变更流程中的字段映射到台账流程中的字段
- `skipFields`: 跳过的字段列表，这些字段不会被更新到台账
- `recordOnly`: 仅记录模式，只添加变更记录，不更新其他字段

## 使用示例

### 1. 合同变更流程配置

```json
{
  "relationField": "contractNo",
  "changeRecordField": "changeDetails",
  "processInstanceIdField": "changeProcessInstanceId",
  "fieldMapping": {
    "handlerUserId": "handler",
    "handlerDept": "handlingDepartment",
    "applyDate": "applicationDate",
    "contractBasicContentNew": "changeContent"
  },
  "skipFields": [
    "handlerUserId",
    "handlerDept",
    "applyDate",
    "contractNo",
    "contractName",
    "customerName",
    "PROCESS_STATUS",
    "PROCESS_START_USER_ID",
    "_FLOWABLE_SKIP_EXPRESSION_ENABLED"
  ]
}
```

### 2. 在BPMN中配置监听器

1. 在BPMN流程设计器中选择流程结束事件
2. 添加执行监听器（ExecutionListener）
3. 配置如下：
   - 监听器类型：`delegateExpression`
   - 监听器表达式：`${bpmGenericFormChangeListener}`
   - 监听事件：`end`
   - 扩展字段名：`listenerConfig`
   - 扩展字段值：上述JSON配置

## 工作流程

1. 变更流程审批通过后，监听器被触发
2. 根据 `relationField` 查找对应的台账流程实例
3. 更新台账流程实例的历史变量：
   - 根据 `fieldMapping` 映射字段
   - 跳过 `skipFields` 中的字段
   - 保存当前变更流程的实例ID到 `processInstanceIdField` 字段
   - 更新申请人、变更时间等特殊字段
4. 如果配置了 `changeRecordField`，则添加变更记录

## 注意事项

1. `processInstanceIdField` 是可选配置，如果不配置则不会保存流程实例ID
2. 流程实例ID会保存为字符串格式
3. 确保台账流程实例中存在对应的字段，否则会跳过更新
4. 建议在测试环境中先验证配置的正确性

## 日志监控

监听器会输出详细的日志信息，包括：
- 配置解析结果
- 台账流程实例查找结果
- 字段更新过程
- 流程实例ID保存结果

可以通过日志来监控和调试监听器的执行情况。
