# 合同变更流程配置示例

## 场景描述

在合同管理系统中，当需要对已有合同进行变更时，需要启动变更审批流程。审批通过后，系统需要：

1. 更新原合同台账的相关信息
2. 记录变更历史
3. **保存变更流程的实例ID，方便后续追溯**

## 配置步骤

### 1. 在BPMN流程中配置监听器

在合同变更流程的结束事件中添加执行监听器：

- **监听器类型**: `delegateExpression`
- **监听器表达式**: `${bpmGenericFormChangeListener}`
- **监听事件**: `end`
- **扩展字段名**: `listenerConfig`
- **扩展字段值**: 见下方JSON配置

### 2. 监听器配置

```json
{
  "relationField": "contractNo",
  "changeRecordField": "changeDetails",
  "processInstanceIdField": "changeProcessInstanceId",
  "fieldMapping": {
    "handlerUserId": "handler",
    "handlerDept": "handlingDepartment",
    "applyDate": "applicationDate",
    "contractBasicContentNew": "changeContent"
  },
  "skipFields": [
    "handlerUserId",
    "handlerDept",
    "applyDate",
    "contractNo",
    "contractName",
    "customerName",
    "PROCESS_STATUS",
    "PROCESS_START_USER_ID",
    "_FLOWABLE_SKIP_EXPRESSION_ENABLED"
  ]
}
```

### 3. 配置说明

#### 关键配置项

- **`relationField: "contractNo"`**: 通过合同编号关联到原合同台账
- **`processInstanceIdField: "changeProcessInstanceId"`**: 将当前变更流程的实例ID保存到台账的这个字段中
- **`changeRecordField: "changeDetails"`**: 变更记录保存到台账的这个字段中

#### 字段映射

- `handlerUserId` → `handler`: 处理人ID映射到处理人字段
- `handlerDept` → `handlingDepartment`: 处理部门映射
- `applyDate` → `applicationDate`: 申请日期映射
- `contractBasicContentNew` → `changeContent`: 新的合同内容映射

#### 跳过字段

这些字段不会被更新到台账中，避免覆盖重要的原始数据。

## 执行效果

当合同变更流程审批通过后，系统会自动：

1. **查找原合同台账**: 根据 `contractNo` 查找对应的合同台账流程实例
2. **更新台账数据**: 根据字段映射更新相关信息
3. **保存流程ID**: 将当前变更流程的实例ID保存到 `changeProcessInstanceId` 字段
4. **记录变更历史**: 在 `changeDetails` 字段中添加本次变更记录
5. **更新时间戳**: 自动更新 `change_time` 和 `update_time`

## 数据追溯

通过保存的 `changeProcessInstanceId`，可以：

- 查询具体的变更流程详情
- 追溯变更的审批过程
- 关联变更申请人和审批人信息
- 查看变更流程的完整历史

## 示例数据

### 变更前台账数据
```json
{
  "contractNo": "*********",
  "contractName": "软件开发合同",
  "customerName": "ABC公司",
  "handler": "张三",
  "changeProcessInstanceId": null
}
```

### 变更后台账数据
```json
{
  "contractNo": "*********",
  "contractName": "软件开发合同",
  "customerName": "ABC公司",
  "handler": "李四",
  "handlingDepartment": "技术部",
  "applicationDate": "2024-01-15",
  "changeContent": "增加新功能模块",
  "changeProcessInstanceId": "proc_inst_12345",
  "change_time": "2024-01-15 14:30:00",
  "update_time": "2024-01-15 14:30:00",
  "changeDetails": [
    {
      "handler": "李四",
      "handlingDepartment": "技术部",
      "applicationDate": "2024-01-15",
      "changeContent": "增加新功能模块",
      "changeTime": "2024-01-15 14:30:00",
      "processInstanceId": "proc_inst_12345"
    }
  ]
}
```

## 注意事项

1. 确保台账流程实例中存在 `changeProcessInstanceId` 字段
2. 字段映射的目标字段必须在台账中存在
3. 建议在测试环境中先验证配置的正确性
4. 可以通过日志监控监听器的执行情况
