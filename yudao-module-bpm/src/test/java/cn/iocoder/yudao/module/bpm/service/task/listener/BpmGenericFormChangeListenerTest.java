package cn.iocoder.yudao.module.bpm.service.task.listener;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BpmGenericFormChangeListener 测试类
 *
 * <AUTHOR>
 */
class BpmGenericFormChangeListenerTest {

    @Test
    @DisplayName("测试监听器配置解析 - 包含流程实例ID字段")
    void testListenerConfigWithProcessInstanceIdField() {
        // 准备测试数据
        String configJson = """
            {
              "relationField": "contractNo",
              "changeRecordField": "changeDetails",
              "processInstanceIdField": "changeProcessInstanceId",
              "fieldMapping": {
                "handlerUserId": "handler",
                "handlerDept": "handlingDepartment"
              },
              "skipFields": [
                "handlerUserId",
                "handlerDept",
                "PROCESS_STATUS"
              ]
            }
            """;

        // 解析配置
        BpmGenericFormChangeListener.ListenerConfig config = 
            JsonUtils.parseObject(configJson, BpmGenericFormChangeListener.ListenerConfig.class);

        // 验证配置
        assertNotNull(config);
        assertEquals("contractNo", config.getRelationField());
        assertEquals("changeDetails", config.getChangeRecordField());
        assertEquals("changeProcessInstanceId", config.getProcessInstanceIdField());
        
        // 验证字段映射
        assertNotNull(config.getFieldMapping());
        assertEquals("handler", config.getFieldMapping().get("handlerUserId"));
        assertEquals("handlingDepartment", config.getFieldMapping().get("handlerDept"));
        
        // 验证跳过字段
        assertNotNull(config.getSkipFields());
        assertTrue(config.getSkipFields().contains("handlerUserId"));
        assertTrue(config.getSkipFields().contains("handlerDept"));
        assertTrue(config.getSkipFields().contains("PROCESS_STATUS"));
    }

    @Test
    @DisplayName("测试监听器配置解析 - 不包含流程实例ID字段")
    void testListenerConfigWithoutProcessInstanceIdField() {
        // 准备测试数据
        String configJson = """
            {
              "relationField": "contractNo",
              "changeRecordField": "changeDetails",
              "fieldMapping": {
                "handlerUserId": "handler"
              }
            }
            """;

        // 解析配置
        BpmGenericFormChangeListener.ListenerConfig config = 
            JsonUtils.parseObject(configJson, BpmGenericFormChangeListener.ListenerConfig.class);

        // 验证配置
        assertNotNull(config);
        assertEquals("contractNo", config.getRelationField());
        assertEquals("changeDetails", config.getChangeRecordField());
        assertNull(config.getProcessInstanceIdField()); // 应该为null
    }

    @Test
    @DisplayName("测试配置toString方法")
    void testListenerConfigToString() {
        BpmGenericFormChangeListener.ListenerConfig config = 
            new BpmGenericFormChangeListener.ListenerConfig();
        config.setRelationField("contractNo");
        config.setProcessInstanceIdField("changeProcessInstanceId");

        String result = config.toString();
        
        assertTrue(result.contains("relationField='contractNo'"));
        assertTrue(result.contains("processInstanceIdField='changeProcessInstanceId'"));
    }
}
